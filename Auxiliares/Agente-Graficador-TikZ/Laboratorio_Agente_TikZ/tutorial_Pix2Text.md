# 📚 TUTORIAL PIX2TEXT - MANJARO PLASMA

## 🎯 **GUÍA COMPLETA PASO A PASO**

### **📋 REQUISITOS PREVIOS**
- ✅ Manjaro Plasma con GTX 1050
- ✅ Entorno virtual `ai_math_tools_gtx1050` instalado
- ✅ Pix2Text instalado y funcionando
- ✅ Script de activación `~/activar_ia_matematicas.sh` creado

---

## 🚀 **PASO 1: ACTIVAR ENTORNO IA**

### **1.1 Abrir Terminal Konsole**
```bash
# Presiona Ctrl+Alt+T o busca "Konsole" en el menú
```

### **1.2 Activar Herramientas IA**
```bash
~/activar_ia_matematicas.sh
```

**✅ Verificación exitosa:**

- Debes ver: "🧮 Activando herramientas IA para matemáticas..."
- Tu prompt debe mostrar: `(ai_math_tools_gtx1050)`
- <PERSON><PERSON><PERSON>: "✅ Entorno activado!"

### **1.3 Verificar Pix2Text**
```bash
python -c "from pix2text import Pix2Text; print('✅ Pix2Text listo')"
```

---

## 📁 **PASO 2: NAVEGACIÓN AL DIRECTORIO CORRECTO**

### **2.1 Ir al Directorio del Proyecto**
```bash
cd ~/Insync/<EMAIL>/Google\ Drive/RepositorioMatematicasICFES_R_Exams/Auxiliares/Agente-Graficador-TikZ/Laboratorio_Agente_TikZ/
```

### **2.2 Verificar Ubicación**
```bash
pwd
```
**Resultado esperado:**
```
/home/<USER>/Insync/<EMAIL>/Google Drive/RepositorioMatematicasICFES_R_Exams/Auxiliares/Agente-Graficador-TikZ/Laboratorio_Agente_TikZ
```

### **2.3 Listar Archivos Disponibles**
```bash
ls -la trapecios*
```
**Archivos que deberías ver:**

- `trapecios.tex` - Código LaTeX fuente
- `trapecios.pdf` - PDF compilado
- `trapecios_FINAL.png` - Imagen para análisis
- `trapecios_FINAL.tikz` - Código TikZ puro

---

## 🖼️ **PASO 3: GENERAR/VERIFICAR IMAGEN**

### **3.1 Si NO tienes trapecios_FINAL.png:**
```bash
# Compilar LaTeX a PDF
pdflatex trapecios.tex

# Convertir PDF a PNG
convert -density 300 trapecios.pdf trapecios_FINAL.png
```

### **3.2 Verificar que la imagen existe:**
```bash
ls -la *.png
file trapecios_FINAL.png
```

### **3.3 Ver información de la imagen:**
```bash
identify trapecios_FINAL.png
```

---

## 🧪 **PASO 4: ANÁLISIS CON PIX2TEXT**

### **4.1 Análisis Básico**
```bash
python -c "
from pix2text import Pix2Text
p2t = Pix2Text()
result = p2t('trapecios_FINAL.png')
print('📊 RESULTADO DEL ANÁLISIS:')
print('=' * 50)
print(result)
print('=' * 50)
"
```

### **4.2 Análisis Detallado con Información**
```bash
python -c "
from pix2text import Pix2Text
import json

# Crear instancia
p2t = Pix2Text()

print('🔍 Analizando imagen: trapecios_FINAL.png')
print('⏳ Procesando...')

# Analizar imagen
result = p2t('trapecios_FINAL.png')

print('\\n📊 RESULTADO COMPLETO:')
print('=' * 60)
print(result)
print('=' * 60)

# Si el resultado es un diccionario, mostrar estructura
if isinstance(result, dict):
    print('\\n📋 ESTRUCTURA DEL RESULTADO:')
    for key, value in result.items():
        print(f'  {key}: {type(value).__name__}')

print('\\n✅ Análisis completado')
"
```

### **4.3 Guardar Resultado en Archivo**
```bash
python -c "
from pix2text import Pix2Text
import datetime

p2t = Pix2Text()
result = p2t('trapecios_FINAL.png')

# Crear archivo con timestamp
timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
filename = f'analisis_pix2text_{timestamp}.txt'

with open(filename, 'w', encoding='utf-8') as f:
    f.write('ANÁLISIS PIX2TEXT - TRAPECIOS\\n')
    f.write('=' * 40 + '\\n')
    f.write(f'Fecha: {datetime.datetime.now()}\\n')
    f.write(f'Imagen: trapecios_FINAL.png\\n\\n')
    f.write('RESULTADO:\\n')
    f.write(str(result))

print(f'✅ Resultado guardado en: {filename}')
"
```

---

## 🎯 **PASO 5: ANÁLISIS DE DIFERENTES TIPOS DE IMÁGENES**

### **5.1 Para Fórmulas Matemáticas**
```bash
python -c "
from pix2text import Pix2Text
p2t = Pix2Text()

# Ejemplo con imagen de fórmula (reemplaza con tu imagen)
# result = p2t('ruta/a/formula.png')
print('💡 Para analizar fórmulas:')
print('result = p2t(\"ruta/a/formula.png\")')
"
```

### **5.2 Para Texto + Fórmulas Mixtas**
```bash
python -c "
from pix2text import Pix2Text
p2t = Pix2Text()

print('💡 Para texto mixto con fórmulas:')
print('result = p2t(\"imagen_mixta.png\")')
print('# Pix2Text detectará automáticamente texto y fórmulas')
"
```

---

## 🔧 **PASO 6: CONFIGURACIONES AVANZADAS**

### **6.1 Configurar Idioma**
```bash
python -c "
from pix2text import Pix2Text

# Configurar para español + matemáticas
p2t = Pix2Text(languages=['es', 'en'])
print('✅ Pix2Text configurado para español e inglés')
"
```

### **6.2 Configurar Calidad de Análisis**
```bash
python -c "
from pix2text import Pix2Text

# Configuración de alta calidad
p2t = Pix2Text(
    analyzer_config={'model_name': 'mfd'},  # Mejor detector de fórmulas
    formula_config={'model_name': 'latex-ocr'}  # Mejor OCR de fórmulas
)
print('✅ Pix2Text configurado para máxima calidad')
"
```

---

## 📊 **PASO 7: INTERPRETACIÓN DE RESULTADOS**

### **7.1 Tipos de Resultados Esperados**

**Para diagramas geométricos (como trapecios):**

- Texto reconocido de las etiquetas (O, P, Q, R, G, H, E, F, k)
- Posible descripción de la estructura
- Coordenadas si son detectables

**Para fórmulas matemáticas:**

- Código LaTeX de las fórmulas
- Texto plano de ecuaciones
- Estructura matemática reconocida

### **7.2 Calidad del Reconocimiento**
- ✅ **Excelente:** Fórmulas claras, texto nítido
- ⚠️ **Buena:** Algunas correcciones menores necesarias
- ❌ **Mejorable:** Imagen borrosa o compleja

---

## 🚨 **SOLUCIÓN DE PROBLEMAS**

### **Error: "No such file or directory"**
```bash
# Verificar ruta actual
pwd
# Verificar que el archivo existe
ls -la *.png
# Usar ruta absoluta si es necesario
python -c "import os; print(os.path.abspath('trapecios_FINAL.png'))"
```

### **Error: "CUDA out of memory"**
```bash
# Ya configurado para CPU, pero si aparece:
export CUDA_VISIBLE_DEVICES=""
# Reiniciar el entorno
deactivate
~/activar_ia_matematicas.sh
```

### **Error: "Module not found"**
```bash
# Verificar que el entorno está activo
which python
# Debería mostrar: /home/<USER>/ai_math_tools_gtx1050/bin/python

# Si no está activo:
~/activar_ia_matematicas.sh
```

---

## 📝 **COMANDOS DE REFERENCIA RÁPIDA**

### **Activación Rápida:**
```bash
~/activar_ia_matematicas.sh
cd ~/Insync/<EMAIL>/Google\ Drive/RepositorioMatematicasICFES_R_Exams/Auxiliares/Agente-Graficador-TikZ/Laboratorio_Agente_TikZ/
```

### **Análisis Rápido:**
```bash
python -c "from pix2text import Pix2Text; print(Pix2Text()('trapecios_FINAL.png'))"
```

### **Desactivación:**
```bash
deactivate
```

---

## 🎉 **¡TUTORIAL COMPLETADO!**

**Ahora tienes todo lo necesario para usar Pix2Text con cualquier imagen matemática en tu Manjaro Plasma con GTX 1050.**

### **📚 Próximos Pasos:**

1. Probar con diferentes tipos de imágenes matemáticas
2. Experimentar con configuraciones avanzadas
3. Integrar con tu workflow de R-exams
4. Crear scripts personalizados para casos específicos

### **🔗 Archivos Relacionados:**

- `instalar_herramientas_ia_gtx1050.sh` - Script de instalación
- `activar_ia_matematicas.sh` - Script de activación
- `INVESTIGACION_HUGGINGFACE_HERRAMIENTAS.md` - Investigación completa

**¡Disfruta usando Pix2Text! 🚀**
